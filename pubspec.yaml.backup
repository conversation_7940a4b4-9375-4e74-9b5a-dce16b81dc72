name: hookup4u
description: A new Flutter application.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.4+13

environment:
  sdk: ">=2.19.6 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  country_code_picker: ^3.0.0
  pin_code_fields: ^8.0.1
  firebase_app_check: ^0.2.1+8
  flutter_paypal: ^0.2.0
  shared_preferences: ^2.0.0
  http: ^0.13.6
  dio: ^5.4.0

  #swipe_stack: 1.0.0
  #flutter_swiper: 1.1.6
  #geocoder: 0.2.1
  flutter_geocoder: ^0.2.2-nullsafety
  flutter_swiper_null_safety: ^1.0.2
  flutter_web_auth: ^0.5.0
  #swipable_stack: ^0.5.0

  intl: ^0.19.0
  firebase_auth: ^4.18.0
  cloud_firestore: ^4.14.0
  flutter_insta: ^1.0.1
  # geolocator: ^6.1.13
  location: ^4.4.0
  image_picker: ^0.8.7+2
  image_cropper: ^3.0.2
  firebase_storage: ^11.6.0
  path_provider: ^2.0.14
  cached_network_image: ^3.2.3
  share: ^2.0.4
  firebase_messaging: ^14.7.11
  flutter_webview_plugin: ^0.4.0
  in_app_purchase: ^3.1.13
  rflutter_alert: ^2.0.7
  agora_rtc_engine: ^5.1.0
  permission_handler: ^10.2.0
  flutter_ringtone_player: ^4.0.0+2
  percent_indicator: ^4.2.3
  carousel_slider: ^4.0.0

  shimmer: ^2.0.0
  #apple_sign_in: ^0.1.0
  url_launcher: ^6.1.10
  flutter_mapbox_autocomplete: ^2.0.0
  # firebase_admob: ^0.11.2
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.5
  easy_localization: ^3.0.3
  firebase_core: ^2.25.4
  google_mobile_ads: ^3.1.0
  flutter_local_notifications: ^16.3.2
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1
  in_app_purchase_storekit: ^0.3.13+1
  change_app_package_name: ^1.1.0
  top_snackbar_flutter: ^3.1.0

  in_app_purchase_android: any
  image: any
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.0

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "asset/Icon/icon.png"
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - asset/auth/
    - asset/
    - asset/translation/

  #  - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Outfit
      fonts:
        - asset: asset/fonts/Outfit-Black.ttf
          weight: 500
        - asset: asset/fonts/Outfit-Bold.ttf
          weight: 700
        - asset: asset/fonts/Outfit-Bold.ttf
          weight: 800
        - asset: asset/fonts/Outfit-Regular.ttf
          weight: 600
        - asset: asset/fonts/Outfit-Regular.ttf
          weight: 400
        - asset: asset/fonts/Outfit-Medium.ttf
          weight: 700
        - asset: asset/fonts/Outfit-Black.ttf
          weight: 900

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
